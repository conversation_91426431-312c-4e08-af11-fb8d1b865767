# BTCV肝脏和胃自动分割项目

本项目使用BTCV数据集实现肝脏(Liver)和胃(Stomach)的自动分割功能，基于MONAI框架和3D U-Net神经网络架构。

## 项目结构

```
ZNTXexperiment2/
├── data/                    # BTCV数据集
│   ├── Training/
│   │   ├── img/            # 训练图像 (img*.nii.gz)
│   │   └── label/          # 训练标签 (label*.nii.gz)
│   └── Testing/
│       └── img/            # 测试图像 (img*.nii.gz)
├── weights/                 # 模型权重保存目录
├── test_outputs/           # 测试结果输出目录
├── main.py                 # 训练脚本
├── dataset.py              # 数据加载和预处理
├── test.py                 # 测试和推理脚本
├── setup.py                # 安装脚本
├── requirements.txt        # 依赖包列表
└── README.md              # 项目说明
```

## 快速开始

### 1. 环境设置

```bash
# 运行安装脚本
python setup.py
```

或者手动安装：

```bash
# 安装依赖包
pip install -r requirements.txt

# 创建输出目录
mkdir -p weights test_outputs
```

### 2. 数据准备

确保BTCV数据集按以下结构组织：

```
data/
├── Training/
│   ├── img/          # 包含 img0001.nii.gz, img0002.nii.gz, ...
│   └── label/        # 包含 label0001.nii.gz, label0002.nii.gz, ...
└── Testing/
    └── img/          # 包含 img0061.nii.gz, img0062.nii.gz, ...
```

### 3. 训练模型

```bash
python main.py
```

训练参数可在`main.py`中调整：
- `NUM_CLASSES = 3`：背景、肝脏、胃
- `BATCH_SIZE = 2`：根据显存调整
- `MAX_EPOCHS = 100`：训练轮数
- `ROI_SIZE = (96, 96, 96)`：训练patch大小

### 4. 测试模型

```bash
python test.py
```

测试脚本会：
- 加载训练好的模型
- 对测试图像进行分割
- 保存分割结果到`test_outputs/`
- 如果有标签，计算Dice分数

### 5. 数据加载测试

```bash
python dataset.py
```

这会测试数据加载器并生成可视化图像。

## 技术细节

### 网络架构
- **模型**: 3D U-Net (MONAI实现)
- **输入**: 单通道3D医学图像
- **输出**: 3类分割图 (背景、肝脏、胃)

### 标签映射
- BTCV原始标签: 肝脏=6, 胃=7
- 映射后标签: 背景=0, 肝脏=1, 胃=2

### 数据预处理
- 重采样到统一间距: (1.5, 1.5, 2.0)mm
- 强度归一化: [-175, 250] → [0, 1]
- 随机裁剪patch: (96, 96, 96)
- 数据增强: 翻转、旋转、强度偏移

### 损失函数
- DiceCELoss: Dice Loss + Cross Entropy Loss
- 适用于类别不平衡的分割任务

## 配置参数

### 训练参数 (main.py)
```python
NUM_CLASSES = 3          # 类别数
BATCH_SIZE = 2           # 批处理大小
LEARNING_RATE = 1e-4     # 学习率
MAX_EPOCHS = 100         # 最大训练轮数
ROI_SIZE = (96, 96, 96)  # patch大小
```

### 测试参数 (test.py)
```python
ROI_SIZE = (96, 96, 96)     # 滑动窗口大小
SW_BATCH_SIZE = 4           # 滑动窗口批处理大小
VISUALIZE_SLICES = True     # 是否可视化结果
```

## 输出文件

### 训练输出
- `weights/best_metric_model.pth`: 最佳模型权重

### 测试输出
- `test_outputs/*_seg.nii.gz`: 分割结果
- `test_outputs/*_visualization.png`: 可视化图像

## 系统要求

- Python 3.8+
- CUDA支持的GPU (推荐)
- 8GB+ GPU显存 (可调整batch_size和patch_size)
- 16GB+ 系统内存

## 依赖包

主要依赖：
- torch >= 1.12.0
- monai >= 1.0.0
- nibabel >= 3.2.0
- numpy >= 1.21.0
- matplotlib >= 3.5.0

完整列表见`requirements.txt`

## 故障排除

### 显存不足
- 减小`BATCH_SIZE`
- 减小`ROI_SIZE`
- 设置`cache_dataset=False`

### 数据加载错误
- 检查数据目录结构
- 确认文件命名格式
- 运行`python dataset.py`测试

### 训练速度慢
- 增加`num_workers`
- 启用`cache_dataset=True`
- 使用更快的存储设备

## 许可证

本项目仅用于学术研究目的。
