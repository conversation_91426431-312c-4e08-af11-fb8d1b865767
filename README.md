# BTCV肝脏和胃自动分割项目

本项目使用BTCV数据集实现肝脏(Liver)和胃(Stomach)的自动分割功能，基于MONAI框架和3D U-Net神经网络架构。

> **注意**: 本项目专注于3D MONAI实现，提供最专业的医学图像分割解决方案。

## 快速开始

### 方法1: 一键启动（推荐）

```bash
python run.py
```

这会显示交互式菜单，包含所有功能选项。

### 方法2: 手动运行

```bash
# 1. 系统演示（推荐先运行）
python demo.py

# 2. 开始训练
python main.py

# 3. 模型测试
python test.py

# 4. 数据可视化
python visualize.py --stats_only
```

## 环境准备

### 1. 安装依赖

```bash
# 自动安装
python setup.py

# 或手动安装
pip install -r requirements.txt
```

主要依赖：
- torch >= 1.12.0
- monai >= 1.0.0
- nibabel >= 3.2.0
- matplotlib >= 3.5.0

### 2. 数据准备

确保BTCV数据集按以下结构组织：

```
data/
├── Training/
│   ├── img/          # 训练图像: img0001.nii.gz, img0002.nii.gz, ...
│   └── label/        # 训练标签: label0001.nii.gz, label0002.nii.gz, ...
└── Testing/
    └── img/          # 测试图像: img0061.nii.gz, img0062.nii.gz, ...
```

## 详细使用步骤

### 步骤1: 验证环境和数据

```bash
python visualize.py --stats_only
```

预期输出：
```
BTCV数据集统计信息
训练图像数量: 30
训练标签数量: 30
测试图像数量: 20
```

### 步骤2: 系统演示

```bash
python demo.py
```

演示内容：
- 数据加载功能测试
- 数据预处理结果展示
- 模型架构信息
- 可视化样例生成

### 步骤3: 模型训练

```bash
python main.py
```

训练过程：
```
使用设备: cpu
正在加载数据...
找到 30 个训练样本
训练集: 24 样本, 验证集: 6 样本
开始训练，共 100 个 epochs...

Epoch 1/100
训练损失: 1.99 → 1.80 (下降中)
每5个epoch进行一次验证
```

### 步骤4: 模型测试

```bash
python test.py
```

测试输出：
- 分割结果: `test_outputs/*.nii.gz`
- 可视化图像: `test_outputs/*.png`
- 性能评估: 平均Dice分数

## 技术细节

### 网络架构
- **模型**: 3D U-Net (MONAI实现)
- **输入**: 单通道3D医学图像
- **输出**: 3类分割图 (背景、肝脏、胃)

### 标签映射
- BTCV原始标签: 肝脏=6, 胃=7
- 映射后标签: 背景=0, 肝脏=1, 胃=2

### 数据预处理
- 重采样到统一间距: (1.5, 1.5, 2.0)mm
- 强度归一化: [-175, 250] → [0, 1]
- 随机裁剪patch: (96, 96, 96)
- 数据增强: 翻转、旋转、强度偏移

### 损失函数
- DiceCELoss: Dice Loss + Cross Entropy Loss
- 适用于类别不平衡的分割任务

## 配置参数

### 训练参数 (main.py)
```python
NUM_CLASSES = 3          # 类别数
BATCH_SIZE = 2           # 批处理大小
LEARNING_RATE = 1e-4     # 学习率
MAX_EPOCHS = 100         # 最大训练轮数
ROI_SIZE = (96, 96, 96)  # patch大小
```

### 测试参数 (test.py)
```python
ROI_SIZE = (96, 96, 96)     # 滑动窗口大小
SW_BATCH_SIZE = 4           # 滑动窗口批处理大小
VISUALIZE_SLICES = True     # 是否可视化结果
```

## 输出文件

### 训练输出
- `weights/best_metric_model.pth`: 最佳模型权重

### 测试输出
- `test_outputs/*_seg.nii.gz`: 分割结果
- `test_outputs/*_visualization.png`: 可视化图像

## 系统要求

- Python 3.8+
- CUDA支持的GPU (推荐)
- 8GB+ GPU显存 (可调整batch_size和patch_size)
- 16GB+ 系统内存

## 依赖包

主要依赖：
- torch >= 1.12.0
- monai >= 1.0.0
- nibabel >= 3.2.0
- numpy >= 1.21.0
- matplotlib >= 3.5.0

完整列表见`requirements.txt`

## 故障排除

### 显存不足
- 减小`BATCH_SIZE`
- 减小`ROI_SIZE`
- 设置`cache_dataset=False`

### 数据加载错误
- 检查数据目录结构
- 确认文件命名格式
- 运行`python dataset.py`测试

### 训练速度慢
- 增加`num_workers`
- 启用`cache_dataset=True`
- 使用更快的存储设备

## 预期结果

### 训练结果
- 训练损失: 从 ~2.0 降至 ~1.5
- 验证Dice: 目标 >0.8
- 训练时间: CPU约2-3小时

### 分割结果
- 肝脏分割精度: 高 (Dice >0.85)
- 胃分割精度: 中等 (Dice 0.6-0.8，胃较小)
- 输出格式: NIfTI (.nii.gz)

## 使用建议

1. **首次使用**: 先运行 `python demo.py` 确认环境
2. **快速测试**: 可以减少 `MAX_EPOCHS` 到 10 进行快速验证
3. **生产使用**: 建议使用GPU并增加训练轮数
4. **结果分析**: 查看 `test_outputs/` 中的可视化结果

## 技术支持

如果遇到问题：
1. 检查数据目录结构是否正确
2. 确认所有依赖包已安装
3. 查看错误日志信息
4. 尝试减小批处理大小

---

**项目特点**: 专业3D医学图像分割，基于MONAI框架，支持肝脏和胃的自动分割。
