import os
import glob
import torch
from monai.data import CacheDataset, DataLoader, Dataset
from monai.transforms import (
    Compose,
    LoadImaged,
    EnsureChannelFirstd,
    Orientationd,
    Spacingd,
    ScaleIntensityRanged,
    CropForegroundd,
    RandCropByPosNegLabeld,
    RandFlipd,
    RandRotate90d,
    RandShiftIntensityd,
    EnsureTyped,
    MapLabelValued,
)
import numpy as np
import nibabel as nib
from skimage.transform import resize

# BTCV数据集中的标签值
# 根据BTCV数据集的标签定义，肝脏的标签值为6，胃的标签值为7
LIVER_BTCV_LABEL = 6
STOMACH_BTCV_LABEL = 7

# 映射到新的标签：0: 背景, 1: 肝脏, 2: 胃
LABEL_MAP = {
    LIVER_BTCV_LABEL: 1,
    STOMACH_BTCV_LABEL: 2,
}

def get_data_loaders(
    data_dir="./data",
    batch_size=2,
    num_workers=4,
    cache_dataset=True,
    cache_rate=0.5,
    spatial_size=(96, 96, 96),
    train_val_split=0.8,
):
    """
    创建训练和验证数据加载器。

    参数:
        data_dir: 数据集根目录
        batch_size: 批处理大小
        num_workers: 数据加载的工作线程数
        cache_dataset: 是否缓存数据集
        cache_rate: 缓存数据集的比例
        spatial_size: 训练时patch的大小
        train_val_split: 训练集和验证集的分割比例

    返回:
        train_loader, val_loader: 训练和验证数据加载器
    """
    # 检查数据目录结构
    train_images_dir = os.path.join(data_dir, "Training", "img")
    train_labels_dir = os.path.join(data_dir, "Training", "label")

    if not os.path.exists(train_images_dir) or not os.path.exists(train_labels_dir):
        raise ValueError(f"训练数据目录 '{train_images_dir}' 或标签目录 '{train_labels_dir}' 不存在！")

    # 获取所有训练图像和标签文件
    train_images = sorted(glob.glob(os.path.join(train_images_dir, "*.nii.gz")))
    train_labels = sorted(glob.glob(os.path.join(train_labels_dir, "*.nii.gz")))

    if len(train_images) == 0 or len(train_labels) == 0:
        raise ValueError(f"在 '{train_images_dir}' 或 '{train_labels_dir}' 中未找到.nii.gz文件！")

    if len(train_images) != len(train_labels):
        raise ValueError(f"训练图像数量 ({len(train_images)}) 与标签数量 ({len(train_labels)}) 不匹配！")

    print(f"找到 {len(train_images)} 个训练样本。")

    # 匹配图像和标签文件（基于文件名）
    data_dicts = []
    for img_path in train_images:
        img_name = os.path.basename(img_path)
        # 从img0001.nii.gz提取数字部分
        img_num = img_name.replace('img', '').replace('.nii.gz', '')

        # 查找对应的标签文件
        label_name = f"label{img_num}.nii.gz"
        label_path = os.path.join(train_labels_dir, label_name)

        if os.path.exists(label_path):
            data_dicts.append({"image": img_path, "label": label_path})
        else:
            print(f"警告: 未找到图像 {img_name} 对应的标签文件 {label_name}")

    if len(data_dicts) == 0:
        raise ValueError("未找到匹配的图像-标签对！")

    print(f"成功匹配 {len(data_dicts)} 个图像-标签对。")

    # 分割训练集和验证集
    num_train = int(len(data_dicts) * train_val_split)
    train_files = data_dicts[:num_train]
    val_files = data_dicts[num_train:]

    print(f"训练集: {len(train_files)} 样本, 验证集: {len(val_files)} 样本")

    # 定义训练时的数据转换
    train_transforms = Compose(
        [
            LoadImaged(keys=["image", "label"]),
            EnsureChannelFirstd(keys=["image", "label"]),
            Orientationd(keys=["image", "label"], axcodes="RAS"),
            Spacingd(
                keys=["image", "label"],
                pixdim=(1.5, 1.5, 2.0),
                mode=("bilinear", "nearest"),
            ),
            # 将原始BTCV标签映射到我们的目标标签
            MapLabelValued(
                keys=["label"],
                orig_labels=list(LABEL_MAP.keys()),
                target_labels=list(LABEL_MAP.values()),
                allow_missing_keys=True
            ),
            ScaleIntensityRanged(
                keys=["image"], a_min=-175, a_max=250,
                b_min=0.0, b_max=1.0, clip=True
            ),
            CropForegroundd(keys=["image", "label"], source_key="image"),
            # 随机裁剪patch进行训练
            RandCropByPosNegLabeld(
                keys=["image", "label"],
                label_key="label",
                spatial_size=spatial_size,
                pos=1,
                neg=1,
                num_samples=4,
                image_key="image",
                image_threshold=0,
            ),
            # 数据增强
            RandFlipd(keys=["image", "label"], prob=0.5, spatial_axis=0),
            RandFlipd(keys=["image", "label"], prob=0.5, spatial_axis=1),
            RandFlipd(keys=["image", "label"], prob=0.5, spatial_axis=2),
            RandRotate90d(keys=["image", "label"], prob=0.5, max_k=3),
            RandShiftIntensityd(keys=["image"], offsets=0.1, prob=0.5),
            EnsureTyped(keys=["image", "label"], dtype="float32"),
        ]
    )

    # 验证时的数据转换 (不包含数据增强)
    val_transforms = Compose(
        [
            LoadImaged(keys=["image", "label"]),
            EnsureChannelFirstd(keys=["image", "label"]),
            Orientationd(keys=["image", "label"], axcodes="RAS"),
            Spacingd(
                keys=["image", "label"],
                pixdim=(1.5, 1.5, 2.0),
                mode=("bilinear", "nearest"),
            ),
            # 将原始BTCV标签映射到我们的目标标签
            MapLabelValued(
                keys=["label"],
                orig_labels=list(LABEL_MAP.keys()),
                target_labels=list(LABEL_MAP.values()),
                allow_missing_keys=True
            ),
            ScaleIntensityRanged(
                keys=["image"], a_min=-175, a_max=250,
                b_min=0.0, b_max=1.0, clip=True
            ),
            CropForegroundd(keys=["image", "label"], source_key="image"),
            EnsureTyped(keys=["image", "label"], dtype="float32"),
        ]
    )

    # 创建数据集
    if cache_dataset:
        print(f"使用CacheDataset，缓存率: {cache_rate}")
        train_ds = CacheDataset(
            data=train_files, transform=train_transforms,
            cache_rate=cache_rate, num_workers=num_workers
        )
        val_ds = CacheDataset(
            data=val_files, transform=val_transforms,
            cache_rate=cache_rate, num_workers=num_workers
        )
    else:
        print("使用普通Dataset，不缓存数据")
        train_ds = Dataset(data=train_files, transform=train_transforms)
        val_ds = Dataset(data=val_files, transform=val_transforms)

    # 创建数据加载器
    train_loader = DataLoader(
        train_ds, batch_size=batch_size, shuffle=True,
        num_workers=num_workers, pin_memory=torch.cuda.is_available()
    )
    val_loader = DataLoader(
        val_ds, batch_size=1, shuffle=False,
        num_workers=num_workers, pin_memory=torch.cuda.is_available()
    )

    return train_loader, val_loader

class BTCVDataset(Dataset):
    def __init__(self, data_dir, transform=None, target_organs=[1, 2]):  # 1: Liver, 2: Stomach
        self.data_dir = data_dir
        self.transform = transform
        self.target_organs = target_organs
        
        self.img_dir = os.path.join(data_dir, 'img')
        self.label_dir = os.path.join(data_dir, 'label')
        
        # 获取所有img和label文件
        img_files = [f for f in os.listdir(self.img_dir) if f.endswith('.nii.gz')]
        label_files = [f for f in os.listdir(self.label_dir) if f.endswith('.nii.gz')]
        
        # 提取编号
        def extract_num(filename):
            return ''.join(filter(str.isdigit, filename))
        img_dict = {extract_num(f): f for f in img_files}
        label_dict = {extract_num(f): f for f in label_files}
        
        # 只保留编号都存在的文件
        common_nums = sorted(list(set(img_dict.keys()) & set(label_dict.keys())))
        if len(common_nums) == 0:
            raise RuntimeError('没有找到img和label编号都一致的样本，请检查数据集文件名！')
        
        self.img_files = [img_dict[num] for num in common_nums]
        self.label_files = [label_dict[num] for num in common_nums]
        
    def __len__(self):
        return len(self.img_files)
    
    def __getitem__(self, idx):
        img_path = os.path.join(self.img_dir, self.img_files[idx])
        label_path = os.path.join(self.label_dir, self.label_files[idx])
        
        # 加载图像和标签
        img = nib.load(img_path).get_fdata()
        label = nib.load(label_path).get_fdata()
        
        # 标准化图像
        img = (img - img.mean()) / img.std()
        
        # 创建多器官分割掩码
        mask = np.zeros_like(label)
        for organ_id in self.target_organs:
            mask[label == organ_id] = 1
            
        # 调整大小为统一尺寸 (256x256)
        img = resize(img, (256, 256), order=1, preserve_range=True)
        mask = resize(mask, (256, 256), order=0, preserve_range=True)
        
        # 转换为PyTorch张量
        img = torch.from_numpy(img).float().unsqueeze(0)  # 添加通道维度
        mask = torch.from_numpy(mask).float().unsqueeze(0)
        
        if self.transform:
            img = self.transform(img)
            mask = self.transform(mask)
            
        return img, mask

if __name__ == "__main__":
    # 测试数据加载器
    import matplotlib.pyplot as plt
    import numpy as np

    train_loader, val_loader = get_data_loaders(
        data_dir="./data",
        batch_size=1,
        num_workers=0,
        cache_dataset=False
    )

    # 获取一个批次的数据
    batch = next(iter(train_loader))
    image, label = batch["image"][0], batch["label"][0]

    # 显示中间切片
    plt.figure("检查", (12, 6))
    plt.subplot(1, 2, 1)
    plt.title("图像")
    plt.imshow(image[0, :, :, image.shape[3]//2].detach().cpu(), cmap="gray")
    plt.subplot(1, 2, 2)
    plt.title("分割标签")
    plt.imshow(label[0, :, :, label.shape[3]//2].detach().cpu())
    plt.savefig("data_check.png")
    print(f"数据检查图像已保存为 'data_check.png'")

    print(f"图像形状: {image.shape}, 标签形状: {label.shape}")
    print(f"标签中的唯一值: {torch.unique(label)}")
