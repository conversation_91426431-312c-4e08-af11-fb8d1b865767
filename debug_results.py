#!/usr/bin/env python3
"""
调试分割结果
"""

import os
import numpy as np
import nibabel as nib
import torch

def debug_segmentation_results():
    """调试分割结果"""
    print("🔍 调试分割结果...")
    
    # 检查第一个分割结果
    seg_file = "./test_outputs/test_image_0_seg.nii.gz"
    if os.path.exists(seg_file):
        seg_img = nib.load(seg_file)
        seg_data = seg_img.get_fdata()
        
        print(f"📄 文件: {seg_file}")
        print(f"📐 形状: {seg_data.shape}")
        print(f"🔢 数据类型: {seg_data.dtype}")
        print(f"📊 唯一值: {np.unique(seg_data)}")
        print(f"📈 值范围: [{seg_data.min()}, {seg_data.max()}]")
        
        # 检查每个通道
        if seg_data.ndim == 4:
            print("\n🔍 各通道分析:")
            for i in range(seg_data.shape[0]):
                channel_data = seg_data[i]
                unique_vals = np.unique(channel_data)
                print(f"   通道 {i}: 形状={channel_data.shape}, 唯一值={unique_vals}")
                
                # 检查是否有非零值
                non_zero_count = np.count_nonzero(channel_data)
                print(f"   通道 {i}: 非零体素数={non_zero_count}")

def debug_model_output():
    """调试模型输出"""
    print("\n🧠 调试模型输出...")
    
    # 重新运行一个简单的推理来检查模型输出
    from monai.networks.nets import UNet
    import torch
    
    # 创建模型
    model = UNet(
        spatial_dims=3,
        in_channels=1,
        out_channels=3,
        channels=(16, 32, 64, 128, 256),
        strides=(2, 2, 2, 2),
        num_res_units=2,
        norm="batch"
    )
    
    # 加载权重
    model_path = "./weights/best_metric_model.pth"
    if os.path.exists(model_path):
        model.load_state_dict(torch.load(model_path, map_location='cpu'))
        model.eval()
        
        # 创建一个测试输入
        test_input = torch.randn(1, 1, 96, 96, 96)  # (B, C, H, W, D)
        
        with torch.no_grad():
            output = model(test_input)
            
        print(f"📥 输入形状: {test_input.shape}")
        print(f"📤 输出形状: {output.shape}")
        print(f"📊 输出范围: [{output.min():.4f}, {output.max():.4f}]")
        
        # 应用softmax
        softmax_output = torch.softmax(output, dim=1)
        print(f"🎯 Softmax后范围: [{softmax_output.min():.4f}, {softmax_output.max():.4f}]")
        
        # 应用argmax
        argmax_output = torch.argmax(softmax_output, dim=1)
        print(f"🎲 Argmax后形状: {argmax_output.shape}")
        print(f"🎲 Argmax后唯一值: {torch.unique(argmax_output)}")
        
        # 检查各类别的概率
        for i in range(3):
            channel_prob = softmax_output[0, i].mean()
            print(f"   类别 {i} 平均概率: {channel_prob:.4f}")

def check_test_transforms():
    """检查测试时的数据变换"""
    print("\n🔄 检查数据变换...")
    
    from test import get_test_transforms
    from monai.data import Dataset
    import glob
    
    # 获取一个测试文件
    test_files = sorted(glob.glob("./data/Testing/img/*.nii.gz"))
    if test_files:
        test_file = [{"image": test_files[0]}]
        
        # 应用变换
        transforms = get_test_transforms(has_labels=False)
        dataset = Dataset(data=test_file, transform=transforms)
        
        try:
            sample = dataset[0]
            print(f"📥 变换后图像形状: {sample['image'].shape}")
            print(f"📊 图像值范围: [{sample['image'].min():.4f}, {sample['image'].max():.4f}]")
            print(f"🔢 图像数据类型: {sample['image'].dtype}")
        except Exception as e:
            print(f"❌ 数据变换错误: {e}")

if __name__ == "__main__":
    debug_segmentation_results()
    debug_model_output()
    check_test_transforms()
