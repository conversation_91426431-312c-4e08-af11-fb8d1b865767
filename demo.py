#!/usr/bin/env python3
"""
BTCV肝脏和胃分割演示脚本
展示数据加载、预处理和可视化功能
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
from dataset import get_data_loaders
import torch

def demo_data_loading():
    """演示数据加载功能"""
    print("=" * 60)
    print("BTCV肝脏和胃分割系统演示")
    print("=" * 60)
    
    print("\n1. 数据加载演示...")
    try:
        train_loader, val_loader = get_data_loaders(
            data_dir="./data",
            batch_size=1,
            num_workers=0,
            cache_dataset=False
        )
        
        print(f"✅ 数据加载成功!")
        print(f"   训练批次数: {len(train_loader)}")
        print(f"   验证批次数: {len(val_loader)}")
        
        return train_loader, val_loader
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None, None

def demo_data_sample(train_loader):
    """演示数据样本"""
    print("\n2. 数据样本演示...")
    
    if train_loader is None:
        print("❌ 无法演示数据样本，数据加载器为空")
        return
    
    try:
        # 获取一个批次
        batch = next(iter(train_loader))
        image = batch["image"][0]  # 取第一个样本
        label = batch["label"][0]
        
        print(f"✅ 数据样本获取成功!")
        print(f"   图像形状: {image.shape}")
        print(f"   标签形状: {label.shape}")
        print(f"   图像数据类型: {image.dtype}")
        print(f"   标签数据类型: {label.dtype}")
        print(f"   图像值范围: [{image.min():.3f}, {image.max():.3f}]")
        print(f"   标签唯一值: {torch.unique(label).tolist()}")
        
        return image, label
        
    except Exception as e:
        print(f"❌ 数据样本获取失败: {e}")
        return None, None

def demo_visualization(image, label):
    """演示数据可视化"""
    print("\n3. 数据可视化演示...")
    
    if image is None or label is None:
        print("❌ 无法进行可视化，数据为空")
        return
    
    try:
        # 转换为numpy数组
        img_np = image[0].cpu().numpy()  # 移除通道维度
        label_np = label[0].cpu().numpy()
        
        # 选择中间切片
        z_mid = img_np.shape[2] // 2
        
        # 创建可视化
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # 原始图像
        axes[0].imshow(img_np[:, :, z_mid], cmap='gray')
        axes[0].set_title(f'原始CT图像 (切片 {z_mid})')
        axes[0].axis('off')
        
        # 标签图像
        axes[1].imshow(label_np[:, :, z_mid], cmap='jet', vmin=0, vmax=2)
        axes[1].set_title('分割标签\n(0:背景, 1:肝脏, 2:胃)')
        axes[1].axis('off')
        
        # 叠加图像
        axes[2].imshow(img_np[:, :, z_mid], cmap='gray')
        mask = label_np[:, :, z_mid] > 0
        axes[2].imshow(np.ma.masked_where(~mask, label_np[:, :, z_mid]), 
                      cmap='jet', alpha=0.5, vmin=0, vmax=2)
        axes[2].set_title('图像+标签叠加')
        axes[2].axis('off')
        
        plt.tight_layout()
        
        # 保存图像
        output_path = "demo_visualization.png"
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 可视化完成!")
        print(f"   保存路径: {output_path}")
        
        # 统计信息
        liver_voxels = np.sum(label_np == 1)
        stomach_voxels = np.sum(label_np == 2)
        total_voxels = label_np.size
        
        print(f"   肝脏体素数: {liver_voxels} ({liver_voxels/total_voxels*100:.2f}%)")
        print(f"   胃体素数: {stomach_voxels} ({stomach_voxels/total_voxels*100:.2f}%)")
        
    except Exception as e:
        print(f"❌ 可视化失败: {e}")

def demo_model_info():
    """演示模型信息"""
    print("\n4. 模型架构演示...")
    
    try:
        from monai.networks.nets import UNet
        
        # 创建模型
        model = UNet(
            spatial_dims=3,
            in_channels=1,
            out_channels=3,
            channels=(16, 32, 64, 128, 256),
            strides=(2, 2, 2, 2),
            num_res_units=2,
            norm="batch"
        )
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"✅ 模型创建成功!")
        print(f"   模型类型: 3D U-Net (MONAI)")
        print(f"   输入通道: 1 (单通道CT图像)")
        print(f"   输出通道: 3 (背景、肝脏、胃)")
        print(f"   总参数数: {total_params:,}")
        print(f"   可训练参数: {trainable_params:,}")
        print(f"   模型大小: ~{total_params*4/1024/1024:.1f} MB")
        
        # 测试前向传播
        dummy_input = torch.randn(1, 1, 96, 96, 96)
        with torch.no_grad():
            output = model(dummy_input)
        
        print(f"   输入形状: {dummy_input.shape}")
        print(f"   输出形状: {output.shape}")
        
    except Exception as e:
        print(f"❌ 模型演示失败: {e}")

def demo_training_config():
    """演示训练配置"""
    print("\n5. 训练配置演示...")
    
    config = {
        "数据集": "BTCV (Beyond the Cranial Vault)",
        "目标器官": ["肝脏 (Liver)", "胃 (Stomach)"],
        "原始标签": "肝脏=6, 胃=7",
        "映射标签": "背景=0, 肝脏=1, 胃=2",
        "网络架构": "3D U-Net",
        "损失函数": "DiceCELoss (Dice + CrossEntropy)",
        "优化器": "Adam",
        "学习率": "1e-4",
        "批处理大小": "2",
        "训练patch大小": "(96, 96, 96)",
        "数据增强": ["随机翻转", "随机旋转", "强度偏移"],
        "预处理": [
            "重采样到(1.5, 1.5, 2.0)mm",
            "强度归一化[-175, 250]→[0, 1]",
            "前景裁剪"
        ]
    }
    
    print("✅ 训练配置:")
    for key, value in config.items():
        if isinstance(value, list):
            print(f"   {key}: {', '.join(value)}")
        else:
            print(f"   {key}: {value}")

def main():
    """主演示函数"""
    # 1. 数据加载演示
    train_loader, val_loader = demo_data_loading()
    
    # 2. 数据样本演示
    image, label = demo_data_sample(train_loader)
    
    # 3. 数据可视化演示
    demo_visualization(image, label)
    
    # 4. 模型信息演示
    demo_model_info()
    
    # 5. 训练配置演示
    demo_training_config()
    
    print("\n" + "=" * 60)
    print("演示完成!")
    print("=" * 60)
    print("\n可用的训练命令:")
    print("  python main.py     # 3D MONAI训练 (推荐)")
    print("  python train.py    # 2D切片训练")
    print("\n可用的测试命令:")
    print("  python test.py     # 模型测试和推理")
    print("\n可用的工具命令:")
    print("  python visualize.py --stats_only  # 数据统计")
    print("  python dataset.py  # 数据加载器测试")

if __name__ == "__main__":
    main()
