import os
import torch
import torch.nn as nn
from torch.optim import Adam
from monai.networks.nets import UNet # 使用MONAI的U-Net
from monai.losses import DiceCELoss  # Dice + CrossEntropy Loss，适用于多类别分割
from monai.metrics import DiceMetric
from monai.inferers import sliding_window_inference # 用于在完整图像上进行推理
from monai.data import decollate_batch # 将批处理数据分离成列表
from monai.transforms import AsDiscrete # 用于将模型输出转换为最终的分割图
from tqdm import tqdm
import numpy as np

from dataset import get_data_loaders # 从之前创建的dataset.py导入

# --- 配置参数 ---
DATA_DIR = "./data"  # BTCV数据集的根目录
MODEL_OUTPUT_DIR = "./weights" # 模型权重保存目录
NUM_CLASSES = 3  # 背景, 肝脏, 胃
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
LEARNING_RATE = 1e-4
MAX_EPOCHS = 100 # 示例，实际可能需要更多
BATCH_SIZE = 2 # 根据显存调整
VAL_INTERVAL = 5 # 每多少个epoch验证一次
ROI_SIZE = (96, 96, 96) # 训练时patch的大小，应与dataset.py中的spatial_size一致
SW_BATCH_SIZE = 4 # sliding_window_inference的批处理大小，加速验证

def main():
    os.makedirs(MODEL_OUTPUT_DIR, exist_ok=True)

    print(f"使用设备: {DEVICE}")

    # 1. 创建数据加载器
    print("正在加载数据...")
    try:
        # 如果显存非常小，在 get_data_loaders 中将 cache_dataset 设置为 False
        # 或者减小 cache_rate。num_workers 可以根据CPU核心数调整。
        train_loader, val_loader = get_data_loaders(
            data_dir=DATA_DIR,
            batch_size=BATCH_SIZE,
            num_workers=4,
            cache_dataset=True, # 开启以加速，如果内存不足则关闭
            cache_rate=0.5 # 缓存50%的数据，根据内存调整
        )
    except Exception as e:
        print(f"加载数据时出错: {e}")
        print("请确保 BTCV 数据集已正确放置在 'DATA_DIR' 指定的路径下，并且 'dataset.py' 文件中的路径和标签配置正确。")
        return

    # 2. 定义网络模型
    # MONAI的UNet参数:
    # spatial_dims=3: 3D U-Net
    # in_channels=1: 输入图像是单通道 (灰度图)
    # out_channels=NUM_CLASSES: 输出通道数等于类别数 (背景+肝脏+胃)，输出的是每个类别的logits
    # channels: U-Net各层的通道数
    # strides: U-Net各层的步长
    # num_res_units: 每个U-Net块中残差单元的数量
    # norm: 使用的归一化层
    model = UNet(
        spatial_dims=3,
        in_channels=1,
        out_channels=NUM_CLASSES,
        channels=(16, 32, 64, 128, 256), # 可以根据显存和性能需求调整
        strides=(2, 2, 2, 2),
        num_res_units=2,
        norm="batch" # 或 "instance"
    ).to(DEVICE)

    # 3. 定义损失函数和优化器
    # DiceCELoss 结合了 Dice Loss 和 CrossEntropy Loss，对于类别不平衡问题通常表现较好
    # to_onehot_y=True: 会将整数标签 [B, 1, H, W, D] 转换为 one-hot 编码 [B, C, H, W, D]
    # softmax=True: 会在计算loss前对网络输出应用softmax
    loss_function = DiceCELoss(to_onehot_y=True, softmax=True)
    optimizer = Adam(model.parameters(), LEARNING_RATE)

    # 4. 定义评估指标
    # include_background=False: 通常我们不评估背景的Dice分数
    # reduction="mean": 计算每个类别Dice的平均值
    dice_metric = DiceMetric(include_background=False, reduction="mean_batch") # 计算每个batch的平均Dice

    # 用于将模型输出 (logits) 转换为最终的类别标签 (0, 1, or 2)
    # argmax=True: 取概率最大的通道作为预测类别
    # to_onehot=NUM_CLASSES: (可选) 如果后续需要one-hot形式的预测图
    post_pred_transform = AsDiscrete(argmax=True, to_onehot_n=NUM_CLASSES)
    # 用于将标签转换为one-hot编码，以匹配DiceMetric的期望输入
    post_label_transform = AsDiscrete(to_onehot_n=NUM_CLASSES)


    # 5. 训练循环
    best_metric = -1
    best_metric_epoch = -1

    print(f"开始训练，共 {MAX_EPOCHS} 个 epochs...")
    for epoch in range(MAX_EPOCHS):
        print("-" * 10)
        print(f"Epoch {epoch + 1}/{MAX_EPOCHS}")
        model.train()
        epoch_loss = 0
        step = 0
        pbar = tqdm(train_loader, desc=f"训练 Epoch {epoch+1}")

        for batch_data in pbar:
            step += 1
            inputs, labels = batch_data["image"].to(DEVICE), batch_data["label"].to(DEVICE)
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = loss_function(outputs, labels)
            loss.backward()
            optimizer.step()
            epoch_loss += loss.item()
            pbar.set_postfix({"loss": loss.item()})
            # print(f"{step}/{len(train_loader) // train_loader.batch_size}, train_loss: {loss.item():.4f}")

        epoch_loss /= step
        print(f"Epoch {epoch + 1} 平均训练损失: {epoch_loss:.4f}")

        # 验证过程
        if (epoch + 1) % VAL_INTERVAL == 0:
            model.eval()
            val_dice_scores = [] # 存储每个验证图像的Dice分数
            print(f"开始验证 Epoch {epoch+1}...")
            with torch.no_grad():
                val_pbar = tqdm(val_loader, desc=f"验证 Epoch {epoch+1}")
                for val_data in val_pbar:
                    val_inputs, val_labels = val_data["image"].to(DEVICE), val_data["label"].to(DEVICE)

                    # 使用滑动窗口推理处理完整的3D图像
                    # roi_size: 滑动窗口的大小，应与训练时的patch大小相关
                    # sw_batch_size: 一次处理多少个窗口，以平衡速度和显存
                    # overlap: 滑动窗口之间的重叠比例，0.5通常是个不错的起点
                    val_outputs = sliding_window_inference(
                        val_inputs, ROI_SIZE, SW_BATCH_SIZE, model, overlap=0.5
                    )

                    # 将输出和标签转换为DiceMetric期望的格式 (B, C, H, W, D)
                    # val_outputs已经是logits，post_pred_transform会做argmax和to_onehot
                    # val_labels是整数标签，post_label_transform会做to_onehot
                    val_outputs_processed = [post_pred_transform(i) for i in decollate_batch(val_outputs)]
                    val_labels_processed = [post_label_transform(i) for i in decollate_batch(val_labels)]

                    # 计算Dice Metric
                    dice_metric(y_pred=val_outputs_processed, y=val_labels_processed)

                # 在所有验证数据上聚合Dice分数
                mean_dice = dice_metric.aggregate().item()
                dice_metric.reset() # 重置metric以备下次使用

                print(f"Epoch {epoch + 1} 平均验证Dice: {mean_dice:.4f}")

                if mean_dice > best_metric:
                    best_metric = mean_dice
                    best_metric_epoch = epoch + 1
                    torch.save(model.state_dict(), os.path.join(MODEL_OUTPUT_DIR, "best_metric_model.pth"))
                    print("已保存新的最佳模型！")
                else:
                    # 如果需要，可以在这里实现早停逻辑
                    pass

                print(
                    f"当前 epoch: {epoch + 1}, 当前平均Dice: {mean_dice:.4f}\n"
                    f"最佳平均Dice: {best_metric:.4f} at epoch: {best_metric_epoch}"
                )
    print("训练完成！")
    print(f"最佳模型保存在: {os.path.join(MODEL_OUTPUT_DIR, 'best_metric_model.pth')}")
    print(f"最佳平均Dice分数: {best_metric:.4f} 在第 {best_metric_epoch} 个epoch达到。")


if __name__ == "__main__":
    # 检查数据目录是否存在
    train_img_dir = os.path.join(DATA_DIR, "Training", "img")
    train_label_dir = os.path.join(DATA_DIR, "Training", "label")

    if not os.path.exists(train_img_dir) or not os.path.exists(train_label_dir):
        print(f"错误：请确保训练数据目录 '{train_img_dir}' 和标签目录 '{train_label_dir}' 存在！")
        print(f"当前数据目录结构应该是：")
        print(f"  {DATA_DIR}/Training/img/  (包含 img*.nii.gz 文件)")
        print(f"  {DATA_DIR}/Training/label/  (包含 label*.nii.gz 文件)")
    else:
        main()