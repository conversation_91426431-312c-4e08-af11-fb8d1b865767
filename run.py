#!/usr/bin/env python3
"""
BTCV肝脏和胃分割项目启动脚本
提供简单的命令行界面来运行不同的功能
"""

import os
import sys
import subprocess

def print_banner():
    """打印项目横幅"""
    print("=" * 60)
    print("BTCV肝脏和胃自动分割项目")
    print("基于3D MONAI框架的医学图像分割")
    print("=" * 60)

def check_environment():
    """检查环境和数据"""
    print("\n检查环境...")

    # 检查数据目录
    data_dirs = [
        "./data/Training/img",
        "./data/Training/label",
        "./data/Testing/img"
    ]

    missing_dirs = []
    for dir_path in data_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)

    if missing_dirs:
        print("缺少数据目录:")
        for dir_path in missing_dirs:
            print(f"   - {dir_path}")
        return False

    # 检查数据文件
    train_imgs = len([f for f in os.listdir("./data/Training/img") if f.endswith('.nii.gz')])
    train_labels = len([f for f in os.listdir("./data/Training/label") if f.endswith('.nii.gz')])

    print(f"环境检查通过")
    print(f"   训练图像: {train_imgs} 个")
    print(f"   训练标签: {train_labels} 个")

    return True

def run_command(cmd, description):
    """运行命令"""
    print(f"\n{description}...")
    print(f"执行命令: {cmd}")
    print("-" * 40)

    try:
        result = subprocess.run(cmd, shell=True, check=True)
        print(f"{description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"{description}失败: {e}")
        return False

def show_menu():
    """显示菜单"""
    print("\n请选择要执行的操作:")
    print("1. 系统演示 (推荐首次运行)")
    print("2. 开始训练")
    print("3. 模型测试")
    print("4. 数据可视化")
    print("5. 检查数据统计")
    print("6. 安装依赖包")
    print("0. 退出")
    print("-" * 40)

def main():
    """主函数"""
    print_banner()

    # 检查环境
    if not check_environment():
        print("\n环境检查失败，请先准备好BTCV数据集")
        print("数据目录结构应该是:")
        print("data/Training/img/     (包含 img*.nii.gz)")
        print("data/Training/label/   (包含 label*.nii.gz)")
        print("data/Testing/img/      (包含 img*.nii.gz)")
        return

    while True:
        show_menu()

        try:
            choice = input("请输入选项 (0-6): ").strip()
        except KeyboardInterrupt:
            print("\n\n再见!")
            break

        if choice == "0":
            print("\n再见!")
            break

        elif choice == "1":
            run_command("python demo.py", "系统演示")

        elif choice == "2":
            print("\n训练将开始，这可能需要较长时间...")
            confirm = input("确认开始训练? (y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                run_command("python main.py", "模型训练")
            else:
                print("取消训练")

        elif choice == "3":
            model_path = "./weights/best_metric_model.pth"
            if os.path.exists(model_path):
                run_command("python test.py", "模型测试")
            else:
                print(f"未找到训练好的模型: {model_path}")
                print("请先运行训练 (选项2)")

        elif choice == "4":
            run_command("python visualize.py", "数据可视化")

        elif choice == "5":
            run_command("python visualize.py --stats_only", "数据统计")

        elif choice == "6":
            run_command("pip install -r requirements.txt", "安装依赖包")

        else:
            print("无效选项，请重新选择")

        # 等待用户按键继续
        if choice != "0":
            input("\n按回车键继续...")

if __name__ == "__main__":
    main()
