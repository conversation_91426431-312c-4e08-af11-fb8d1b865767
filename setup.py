#!/usr/bin/env python3
"""
BTCV肝脏和胃分割项目安装脚本
"""

import subprocess
import sys
import os

def install_requirements():
    """安装必要的依赖包"""
    print("正在安装依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("依赖包安装成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"安装依赖包失败: {e}")
        return False

def check_data_structure():
    """检查数据目录结构"""
    print("检查数据目录结构...")
    
    data_dir = "./data"
    required_dirs = [
        os.path.join(data_dir, "Training", "img"),
        os.path.join(data_dir, "Training", "label"),
        os.path.join(data_dir, "Testing", "img")
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print("警告: 以下目录不存在:")
        for dir_path in missing_dirs:
            print(f"  - {dir_path}")
        print("\n请确保BTCV数据集按以下结构组织:")
        print("data/")
        print("├── Training/")
        print("│   ├── img/          (包含 img*.nii.gz 文件)")
        print("│   └── label/        (包含 label*.nii.gz 文件)")
        print("└── Testing/")
        print("    └── img/          (包含 img*.nii.gz 文件)")
        return False
    else:
        print("数据目录结构正确！")
        return True

def create_output_dirs():
    """创建输出目录"""
    print("创建输出目录...")
    dirs_to_create = ["./weights", "./test_outputs"]
    
    for dir_path in dirs_to_create:
        os.makedirs(dir_path, exist_ok=True)
        print(f"创建目录: {dir_path}")

def main():
    print("=" * 50)
    print("BTCV肝脏和胃分割项目设置")
    print("=" * 50)
    
    # 1. 安装依赖
    if not install_requirements():
        print("设置失败：无法安装依赖包")
        return False
    
    # 2. 创建输出目录
    create_output_dirs()
    
    # 3. 检查数据结构
    data_ok = check_data_structure()
    
    print("\n" + "=" * 50)
    if data_ok:
        print("设置完成！您现在可以:")
        print("1. 运行训练: python main.py")
        print("2. 运行测试: python test.py")
        print("3. 测试数据加载: python dataset.py")
    else:
        print("设置完成，但请先正确组织数据目录结构")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    main()
