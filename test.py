import os
import glob
import torch
import numpy as np
import nibabel as nib # 用于保存NIfTI文件
from tqdm import tqdm
import matplotlib.pyplot as plt

from monai.networks.nets import UNet
from monai.inferers import sliding_window_inference
from monai.data import Dataset, DataLoader, decollate_batch
from monai.transforms import (
    Compose,
    LoadImaged,
    EnsureChannelFirstd,
    Orientationd,
    Spacingd,
    ScaleIntensityRanged,
    CropForegroundd,
    EnsureTyped,
    AsDiscrete,
    MapLabelValued # 确保这个导入和dataset.py中的一致
)
from monai.metrics import DiceMetric
from monai.utils import set_determinism

# 从dataset.py导入标签定义 (或者在这里重新定义以保持独立性)
# 为了让脚本更独立，我们可以在这里复制必要的定义
# (请根据你的BTCV数据集实际情况修改标签值)
LIVER_BTCV_LABEL = 6
STOMACH_BTCV_LABEL = 7

# 映射到新的标签：0: 背景, 1: 肝脏, 2: 胃
LABEL_MAP = {
    LIVER_BTCV_LABEL: 1,
    STOMACH_BTCV_LABEL: 2,
}

# --- 配置参数 ---
# **重要**: 修改这些路径和参数以匹配你的设置
MODEL_PATH = "./weights/best_metric_model.pth" # 训练好的模型权重路径
TEST_DATA_DIR = "./data" # 包含测试图像的目录，例如包含 imagesTs 和可选的 labelsTs
OUTPUT_DIR = "./test_outputs" # 保存分割结果的目录
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

NUM_CLASSES = 3  # 背景, 肝脏, 胃 (与训练时一致)
ROI_SIZE = (96, 96, 96) # 滑动窗口的大小，应与训练时相关或根据推理需求调整
SW_BATCH_SIZE = 4 # sliding_window_inference的批处理大小

# 是否有测试集标签可用于评估 (如果没有，则只进行预测)
HAS_TEST_LABELS = True # 如果有 labelsTs 文件夹，则设为True

# 可视化设置
VISUALIZE_SLICES = True # 是否可视化中间切片
NUM_SLICES_TO_VISUALIZE = 5 # 可视化多少个样本的切片

# 设定随机种子以便复现 (主要影响某些随机过程，推理时影响较小)
set_determinism(seed=42)

def get_test_transforms():
    """
    定义测试时的数据转换流程。
    通常不包含数据增强，但包含必要的预处理步骤。
    """
    keys = ["image"]
    if HAS_TEST_LABELS:
        keys.append("label")

    pixdim = (1.5, 1.5, 2.0) # 与训练时一致的体素重采样目标间距

    load_transforms = [
        LoadImaged(keys=keys, image_only=False if HAS_TEST_LABELS else True),
        EnsureChannelFirstd(keys=keys),
        Orientationd(keys=keys, axcodes="RAS"),
        Spacingd(keys=keys, pixdim=pixdim, mode=("bilinear", "nearest" if HAS_TEST_LABELS else "bilinear")),
    ]

    map_label_transform = []
    if HAS_TEST_LABELS:
        map_label_transform = [
            MapLabelValued(
                keys=["label"],
                orig_labels=list(LABEL_MAP.keys()),
                target_labels=list(LABEL_MAP.values()),
                allow_missing_keys=True
            )
        ]

    common_transforms = [
        ScaleIntensityRanged(
            keys=["image"], a_min=-175, a_max=250,
            b_min=0.0, b_max=1.0, clip=True
        ),
        CropForegroundd(keys=["image"], source_key="image"), # 只基于图像裁剪
        EnsureTyped(keys=["image"], dtype="float32") # 确保图像数据类型
    ]
    if HAS_TEST_LABELS:
        common_transforms.append(EnsureTyped(keys=["label"], dtype="int64")) # 标签通常是整数

    return Compose(load_transforms + map_label_transform + common_transforms)

def visualize_one_slice(image_path, image_tensor, label_tensor, pred_tensor, output_filename_prefix):
    """
    可视化单个样本的中间切片：原始图像、真实标签（如果提供）、预测标签。
    image_tensor, label_tensor, pred_tensor 应该是3D张量 (C, H, W, D)，这里取中间的D切片。
    """
    image = image_tensor.cpu().numpy().squeeze() # (H, W, D)
    pred = pred_tensor.cpu().numpy().squeeze()   # (H, W, D)

    slice_idx = image.shape[2] // 2 # 取Z轴中间的切片

    num_plots = 2
    if label_tensor is not None:
        label = label_tensor.cpu().numpy().squeeze() # (H, W, D)
        num_plots = 3

    fig, axes = plt.subplots(1, num_plots, figsize=(15 if num_plots==3 else 10, 5))
    fig.suptitle(f"Visualizing Slice {slice_idx} of {os.path.basename(image_path)}")

    axes[0].imshow(image[:, :, slice_idx], cmap="gray")
    axes[0].set_title("Input Image")
    axes[0].axis("off")

    current_ax_idx = 1
    if label_tensor is not None:
        axes[current_ax_idx].imshow(label[:, :, slice_idx], cmap="jet", vmin=0, vmax=NUM_CLASSES-1)
        axes[current_ax_idx].set_title("Ground Truth Label")
        axes[current_ax_idx].axis("off")
        current_ax_idx += 1

    axes[current_ax_idx].imshow(pred[:, :, slice_idx], cmap="jet", vmin=0, vmax=NUM_CLASSES-1)
    axes[current_ax_idx].set_title("Predicted Segmentation")
    axes[current_ax_idx].axis("off")

    plt.tight_layout(rect=[0, 0, 1, 0.96]) # 调整布局以适应标题
    save_path = os.path.join(OUTPUT_DIR, f"{output_filename_prefix}_slice_{slice_idx}_visualization.png")
    plt.savefig(save_path)
    print(f"可视化结果已保存到: {save_path}")
    plt.close(fig)


def main():
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    print(f"使用设备: {DEVICE}")

    # 1. 加载测试数据
    test_image_files = sorted(glob.glob(os.path.join(TEST_DATA_DIR, "imagesTs", "*.nii.gz")))
    if not test_image_files:
        print(f"错误: 在 {os.path.join(TEST_DATA_DIR, 'imagesTs')} 中未找到测试图像。请检查路径。")
        # 尝试从训练集/验证集加载数据进行测试 (如果imagesTs为空)
        print(f"尝试从 {os.path.join(TEST_DATA_DIR, 'imagesTr')} 加载图像进行测试...")
        test_image_files = sorted(glob.glob(os.path.join(TEST_DATA_DIR, "imagesTr", "*.nii.gz")))
        if not test_image_files:
            print(f"错误: 在 {os.path.join(TEST_DATA_DIR, 'imagesTr')} 中也未找到图像。请提供测试图像。")
            return
        print(f"将使用 {os.path.join(TEST_DATA_DIR, 'imagesTr')} 中的图像进行测试。")


    test_files = [{"image": img_path} for img_path in test_image_files]

    if HAS_TEST_LABELS:
        test_label_files = sorted(glob.glob(os.path.join(TEST_DATA_DIR, "labelsTs", "*.nii.gz")))
        if not test_label_files and len(test_image_files) > 0 : # 如果 labelsTs 为空，但 imagesTs 非空
            print(f"警告: 在 {os.path.join(TEST_DATA_DIR, 'labelsTs')} 中未找到测试标签。将仅进行预测，不进行评估。")
            global HAS_TEST_LABELS # 修改全局变量
            HAS_TEST_LABELS = False
            # 重新生成 test_files，因为 keys 变了
            test_files = [{"image": img_path} for img_path in test_image_files]
        elif len(test_image_files) != len(test_label_files):
            print("警告: 测试图像和标签数量不匹配。请检查数据。将尝试匹配文件名。")
            # 尝试基于文件名匹配 (更鲁棒的方式是解析文件名并配对)
            # 这里简化处理，假设顺序对应或需要手动调整
            # 对于 BTCV，文件名通常是对应的，如 case_00X.nii.gz
            # 如果不匹配，评估可能会出错或不准确
            min_len = min(len(test_image_files), len(test_label_files))
            test_image_files = test_image_files[:min_len]
            test_label_files = test_label_files[:min_len]
            test_files = [{"image": img, "label": lbl} for img, lbl in zip(test_image_files, test_label_files)]
        else:
            test_files = [{"image": img, "label": lbl} for img, lbl in zip(test_image_files, test_label_files)]


    if not test_files:
        print("没有找到可供测试的文件。")
        return

    print(f"找到 {len(test_files)} 个测试样本。")

    test_transforms = get_test_transforms()
    # 使用普通的Dataset，因为测试集通常较小，或者我们逐个处理
    # 注意：如果测试时也遇到OOM，可以考虑CacheDataset或调整ROI_SIZE/SW_BATCH_SIZE
    test_ds = Dataset(data=test_files, transform=test_transforms)
    # 测试时batch_size通常为1，因为我们要对每个完整图像进行推理
    test_loader = DataLoader(test_ds, batch_size=1, shuffle=False, num_workers=4, pin_memory=True)


    # 2. 定义并加载模型
    model = UNet(
        spatial_dims=3,
        in_channels=1,
        out_channels=NUM_CLASSES,
        channels=(16, 32, 64, 128, 256), # 确保与训练时模型结构一致
        strides=(2, 2, 2, 2),
        num_res_units=2,
        norm="batch"
    ).to(DEVICE)

    if not os.path.exists(MODEL_PATH):
        print(f"错误: 模型权重文件 {MODEL_PATH} 不存在。请先训练模型。")
        return

    try:
        model.load_state_dict(torch.load(MODEL_PATH, map_location=DEVICE))
        print(f"模型权重从 {MODEL_PATH} 加载成功。")
    except Exception as e:
        print(f"加载模型权重失败: {e}")
        return

    model.eval() # 设置为评估模式

    # 3. 定义评估指标和后处理 (如果需要评估)
    dice_metric = None
    post_pred_transform = AsDiscrete(argmax=True, to_onehot_n=NUM_CLASSES) # 用于DiceMetric
    post_label_transform = AsDiscrete(to_onehot_n=NUM_CLASSES) # 用于DiceMetric
    # 用于保存或可视化的最终分割图 (不需要one-hot)
    final_segmentation_transform = AsDiscrete(argmax=True, to_onehot_n=None)


    if HAS_TEST_LABELS:
        dice_metric = DiceMetric(include_background=False, reduction="mean_batch")

    # 4. 推理循环
    all_preds_meta_dict = [] # 存储元数据以保存文件

    with torch.no_grad():
        pbar = tqdm(test_loader, desc="测试中")
        for i, test_data in enumerate(pbar):
            test_inputs = test_data["image"].to(DEVICE)
            # 保存原始图像的元数据，特别是仿射矩阵，以便正确保存分割结果
            original_affine = test_data["image_meta_dict"]["original_affine"][0].cpu().numpy()
            original_filename = test_data["image_meta_dict"]["filename_or_obj"][0]
            output_filename_base = os.path.splitext(os.path.splitext(os.path.basename(original_filename))[0])[0] # 移除 .nii.gz

            # 滑动窗口推理
            test_outputs_logits = sliding_window_inference(
                test_inputs, ROI_SIZE, SW_BATCH_SIZE, model, overlap=0.5, device=DEVICE, progress=False
            )

            # 获取最终的分割类别图 (B, 1, H, W, D) 或 (1, H, W, D)
            # 注意: test_outputs_logits 已经是 [B, C, H, W, D]
            # final_segmentation_transform 会进行 argmax
            predicted_segmentation = final_segmentation_transform(test_outputs_logits.cpu())[0] # 取第一个batch item, 形状 (1, H, W, D) 或 (H,W,D)
            predicted_segmentation = predicted_segmentation.squeeze() # 确保是 (H,W,D)

            # 保存分割结果为NIfTI文件
            output_nifti_path = os.path.join(OUTPUT_DIR, f"{output_filename_base}_seg.nii.gz")
            # 使用原始图像的仿射矩阵保存，以确保空间对齐
            nib.save(nib.Nifti1Image(predicted_segmentation.numpy().astype(np.uint8), affine=original_affine), output_nifti_path)
            print(f"分割结果已保存到: {output_nifti_path}")

            # 如果有标签，则计算指标
            if HAS_TEST_LABELS and dice_metric is not None:
                test_labels = test_data["label"].to(DEVICE) # (B, 1, H, W, D)
                # DiceMetric 需要 one-hot 格式
                test_outputs_onehot = [post_pred_transform(logit_batch) for logit_batch in decollate_batch(test_outputs_logits)]
                test_labels_onehot = [post_label_transform(label_batch) for label_batch in decollate_batch(test_labels)]
                dice_metric(y_pred=test_outputs_onehot, y=test_labels_onehot)

            # 可视化
            if VISUALIZE_SLICES and i < NUM_SLICES_TO_VISUALIZE:
                label_to_viz = test_data["label"][0] if HAS_TEST_LABELS else None # (1, H, W, D)
                visualize_one_slice(
                    original_filename,
                    test_inputs[0], # (C, H, W, D)
                    label_to_viz,
                    predicted_segmentation.unsqueeze(0), # (1, H, W, D)
                    output_filename_base
                )

    # 聚合和打印指标
    if HAS_TEST_LABELS and dice_metric is not None:
        mean_dice = dice_metric.aggregate().item()
        # 如果想看每个类的Dice，可以这样获取 (假设 DiceMetric 返回的是一个列表或字典)
        # individual_dice_scores = dice_metric.aggregate() # 这可能返回一个tensor，需要进一步处理
        print("-" * 20)
        print(f"测试集平均 Dice (肝脏和胃): {mean_dice:.4f}")
        # MONAI 的 DiceMetric 在 reduction="mean_batch" 后 aggregate() 会给出所有非背景类的平均值。
        # 如果想分别看肝脏（类别1）和胃（类别2）的Dice，你需要调整DiceMetric的reduction参数，
        # 或者在每个batch后手动提取特定类别的dice。
        # 例如，可以设置 reduction=None，然后手动平均每个类别：
        # dice_metric_per_class = DiceMetric(include_background=False, reduction=None)
        # ... (在循环中调用 dice_metric_per_class) ...
        # aggregated_scores = dice_metric_per_class.aggregate() # (num_classes, )
        # liver_dice = aggregated_scores[0].item() # 假设映射后肝脏是第一个非背景类
        # stomach_dice = aggregated_scores[1].item() # 假设映射后胃是第二个非背景类
        # print(f"肝脏 (类别1) Dice: {liver_dice:.4f}")
        # print(f"胃 (类别2) Dice: {stomach_dice:.4f}")
        dice_metric.reset()

    print("测试完成！")

if __name__ == "__main__":
    # 检查模型和数据目录
    if not os.path.exists(MODEL_PATH):
        print(f"错误: 模型权重 '{MODEL_PATH}' 未找到。请确保路径正确或已训练模型。")
    elif not os.path.exists(os.path.join(TEST_DATA_DIR, "imagesTs")) and \
         not os.path.exists(os.path.join(TEST_DATA_DIR, "imagesTr")): # 检查imagesTr作为备选
        print(f"错误: 测试图像目录 '{os.path.join(TEST_DATA_DIR, 'imagesTs')}' (或 'imagesTr') 未找到。")
    else:
        main()