#!/usr/bin/env python3
"""
数据和结果可视化脚本
用于检查BTCV数据集和分割结果
"""

import os
import glob
import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
import argparse

def load_nifti(file_path):
    """加载NIfTI文件"""
    try:
        nii = nib.load(file_path)
        data = nii.get_fdata()
        return data, nii.affine
    except Exception as e:
        print(f"加载文件失败 {file_path}: {e}")
        return None, None

def visualize_slices(image, label=None, prediction=None, title="", save_path=None):
    """可视化3D图像的中间切片"""
    if image is None:
        return
    
    # 获取中间切片索引
    z_mid = image.shape[2] // 2
    y_mid = image.shape[1] // 2
    x_mid = image.shape[0] // 2
    
    # 设置子图数量
    num_plots = 1
    if label is not None:
        num_plots += 1
    if prediction is not None:
        num_plots += 1
    
    fig, axes = plt.subplots(3, num_plots, figsize=(5*num_plots, 12))
    if num_plots == 1:
        axes = axes.reshape(-1, 1)
    
    # 显示图像
    axes[0, 0].imshow(image[:, :, z_mid].T, cmap='gray', origin='lower')
    axes[0, 0].set_title(f'{title} - 轴状面 (Z={z_mid})')
    axes[0, 0].axis('off')
    
    axes[1, 0].imshow(image[:, y_mid, :].T, cmap='gray', origin='lower')
    axes[1, 0].set_title(f'{title} - 冠状面 (Y={y_mid})')
    axes[1, 0].axis('off')
    
    axes[2, 0].imshow(image[x_mid, :, :].T, cmap='gray', origin='lower')
    axes[2, 0].set_title(f'{title} - 矢状面 (X={x_mid})')
    axes[2, 0].axis('off')
    
    col_idx = 1
    
    # 显示标签
    if label is not None:
        axes[0, col_idx].imshow(label[:, :, z_mid].T, cmap='jet', origin='lower', vmin=0, vmax=2)
        axes[0, col_idx].set_title('真实标签 - 轴状面')
        axes[0, col_idx].axis('off')
        
        axes[1, col_idx].imshow(label[:, y_mid, :].T, cmap='jet', origin='lower', vmin=0, vmax=2)
        axes[1, col_idx].set_title('真实标签 - 冠状面')
        axes[1, col_idx].axis('off')
        
        axes[2, col_idx].imshow(label[x_mid, :, :].T, cmap='jet', origin='lower', vmin=0, vmax=2)
        axes[2, col_idx].set_title('真实标签 - 矢状面')
        axes[2, col_idx].axis('off')
        
        col_idx += 1
    
    # 显示预测
    if prediction is not None:
        axes[0, col_idx].imshow(prediction[:, :, z_mid].T, cmap='jet', origin='lower', vmin=0, vmax=2)
        axes[0, col_idx].set_title('预测分割 - 轴状面')
        axes[0, col_idx].axis('off')
        
        axes[1, col_idx].imshow(prediction[:, y_mid, :].T, cmap='jet', origin='lower', vmin=0, vmax=2)
        axes[1, col_idx].set_title('预测分割 - 冠状面')
        axes[1, col_idx].axis('off')
        
        axes[2, col_idx].imshow(prediction[x_mid, :, :].T, cmap='jet', origin='lower', vmin=0, vmax=2)
        axes[2, col_idx].set_title('预测分割 - 矢状面')
        axes[2, col_idx].axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"可视化结果保存到: {save_path}")
    else:
        plt.show()
    
    plt.close()

def check_data_statistics(data_dir="./data"):
    """检查数据集统计信息"""
    print("=" * 50)
    print("BTCV数据集统计信息")
    print("=" * 50)
    
    # 训练集
    train_img_dir = os.path.join(data_dir, "Training", "img")
    train_label_dir = os.path.join(data_dir, "Training", "label")
    
    train_images = sorted(glob.glob(os.path.join(train_img_dir, "*.nii.gz")))
    train_labels = sorted(glob.glob(os.path.join(train_label_dir, "*.nii.gz")))
    
    print(f"训练图像数量: {len(train_images)}")
    print(f"训练标签数量: {len(train_labels)}")
    
    # 测试集
    test_img_dir = os.path.join(data_dir, "Testing", "img")
    test_images = sorted(glob.glob(os.path.join(test_img_dir, "*.nii.gz")))
    print(f"测试图像数量: {len(test_images)}")
    
    # 检查第一个训练样本的详细信息
    if train_images and train_labels:
        print("\n第一个训练样本信息:")
        img_data, _ = load_nifti(train_images[0])
        label_data, _ = load_nifti(train_labels[0])
        
        if img_data is not None:
            print(f"图像形状: {img_data.shape}")
            print(f"图像数据类型: {img_data.dtype}")
            print(f"图像值范围: [{img_data.min():.2f}, {img_data.max():.2f}]")
        
        if label_data is not None:
            print(f"标签形状: {label_data.shape}")
            print(f"标签数据类型: {label_data.dtype}")
            unique_labels = np.unique(label_data)
            print(f"标签中的唯一值: {unique_labels}")
            
            # 统计各类别的体素数量
            for label_val in unique_labels:
                count = np.sum(label_data == label_val)
                percentage = count / label_data.size * 100
                label_name = {0: "背景", 6: "肝脏", 7: "胃"}.get(label_val, f"未知({label_val})")
                print(f"  {label_name}: {count} 体素 ({percentage:.2f}%)")

def visualize_sample(data_dir="./data", sample_idx=0, output_dir="./visualizations"):
    """可视化指定的样本"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取训练样本
    train_img_dir = os.path.join(data_dir, "Training", "img")
    train_label_dir = os.path.join(data_dir, "Training", "label")
    
    train_images = sorted(glob.glob(os.path.join(train_img_dir, "*.nii.gz")))
    train_labels = sorted(glob.glob(os.path.join(train_label_dir, "*.nii.gz")))
    
    if sample_idx >= len(train_images):
        print(f"样本索引 {sample_idx} 超出范围 (最大: {len(train_images)-1})")
        return
    
    # 加载数据
    img_path = train_images[sample_idx]
    label_path = train_labels[sample_idx]
    
    print(f"可视化样本: {os.path.basename(img_path)}")
    
    img_data, _ = load_nifti(img_path)
    label_data, _ = load_nifti(label_path)
    
    if img_data is None or label_data is None:
        print("加载数据失败")
        return
    
    # 映射标签 (6->1, 7->2, 其他->0)
    mapped_label = np.zeros_like(label_data)
    mapped_label[label_data == 6] = 1  # 肝脏
    mapped_label[label_data == 7] = 2  # 胃
    
    # 可视化
    sample_name = os.path.basename(img_path).replace('.nii.gz', '')
    save_path = os.path.join(output_dir, f"{sample_name}_visualization.png")
    
    visualize_slices(
        img_data, 
        mapped_label, 
        title=f"样本 {sample_name}",
        save_path=save_path
    )

def main():
    parser = argparse.ArgumentParser(description="BTCV数据可视化工具")
    parser.add_argument("--data_dir", default="./data", help="数据目录路径")
    parser.add_argument("--output_dir", default="./visualizations", help="输出目录路径")
    parser.add_argument("--sample_idx", type=int, default=0, help="要可视化的样本索引")
    parser.add_argument("--stats_only", action="store_true", help="仅显示统计信息")
    
    args = parser.parse_args()
    
    # 检查数据统计
    check_data_statistics(args.data_dir)
    
    if not args.stats_only:
        print(f"\n可视化样本 {args.sample_idx}...")
        visualize_sample(args.data_dir, args.sample_idx, args.output_dir)

if __name__ == "__main__":
    main()
