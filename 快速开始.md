# 🚀 BTCV肝脏和胃分割 - 快速开始

## 一键启动

```bash
# 使用项目启动脚本（推荐）
python run.py
```

## 手动运行

```bash
# 1. 系统演示
python demo.py

# 2. 开始训练
python main.py

# 3. 模型测试
python test.py
```

## 数据要求

确保数据结构如下：
```
data/
├── Training/
│   ├── img/          # img0001.nii.gz, img0002.nii.gz, ...
│   └── label/        # label0001.nii.gz, label0002.nii.gz, ...
└── Testing/
    └── img/          # img0061.nii.gz, img0062.nii.gz, ...
```

## 预期结果

- **训练**: 生成 `weights/best_metric_model.pth`
- **测试**: 生成 `test_outputs/*.nii.gz` 分割结果
- **性能**: 平均Dice分数 >0.8

## 问题排查

1. **依赖问题**: `pip install -r requirements.txt`
2. **内存不足**: 减小 `BATCH_SIZE` 到 1
3. **CUDA问题**: 程序自动使用CPU训练

详细说明请查看 `运行指南.md`
