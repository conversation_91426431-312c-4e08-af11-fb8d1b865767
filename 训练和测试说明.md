# 训练和测试详细说明

## 🎯 main.py 训练数据使用

### 数据分割策略
**main.py 不会使用所有Training数据进行训练**，而是会自动分割：

```python
# 默认分割比例 (dataset.py 第98行)
train_val_split = 0.8  # 80%训练，20%验证

# 实际分割
num_train = int(len(data_dicts) * 0.8)
train_files = data_dicts[:num_train]    # 前80%用于训练
val_files = data_dicts[num_train:]      # 后20%用于验证
```

### 具体数据使用情况
以您的30个Training样本为例：
- **训练集**: 24个样本 (img0001-img0024)
- **验证集**: 6个样本 (img0025-img0030)
- **测试集**: 20个Testing样本 (用于最终测试)

### 训练过程
```
训练阶段:
├── 使用24个样本进行模型训练
├── 每个样本随机裁剪4个patch (96×96×96)
├── 应用数据增强 (翻转、旋转、强度变化)
└── 每5个epoch用6个验证样本评估性能

验证阶段:
├── 使用6个样本计算Dice分数
├── 保存最佳模型 (best_metric_model.pth)
└── 监控过拟合
```

## 🔬 test.py 输出内容

### 1. 分割结果文件
```
test_outputs/
├── img0061_seg.nii.gz    # 测试图像的分割结果
├── img0062_seg.nii.gz
├── ...
└── img0080_seg.nii.gz
```

**分割结果说明**:
- 格式: NIfTI (.nii.gz)
- 数值编码:
  - 0 = 背景
  - 1 = 肝脏
  - 2 = 胃
- 与原始图像空间对齐

### 2. 可视化图像
```
test_outputs/
├── img0061_visualization.png    # 可视化对比图
├── img0062_visualization.png
└── ...
```

**可视化内容**:
- 原始CT图像 (灰度)
- 真实标签 (如果有)
- 预测分割 (彩色编码)
- 三视图: 轴状面、冠状面、矢状面

### 3. 性能评估指标
```
控制台输出:
----------------------------------------
测试图像: img0061.nii.gz
分割结果已保存到: test_outputs/img0061_seg.nii.gz
可视化已保存到: test_outputs/img0061_visualization.png
----------------------------------------
...
测试集平均 Dice (肝脏和胃): 0.8234
测试完成！
```

**Dice分数说明**:
- 范围: 0-1 (1为完美分割)
- 计算: 2×(预测∩真实)/(预测+真实)
- 评估肝脏和胃的综合分割精度

### 4. 详细输出示例

运行 `python test.py` 的完整输出：

```bash
使用设备: cpu
加载模型: ./weights/best_metric_model.pth
模型加载成功！

使用测试集图像进行推理。
找到 20 个测试图像

开始测试...
----------------------------------------
测试图像 1/20: img0061.nii.gz
原始图像形状: (512, 512, 147)
预处理后形状: torch.Size([1, 1, 384, 384, 147])
正在进行滑动窗口推理...
分割结果已保存到: test_outputs/img0061_seg.nii.gz
可视化已保存到: test_outputs/img0061_visualization.png
----------------------------------------
测试图像 2/20: img0062.nii.gz
...
----------------------------------------
测试集平均 Dice (肝脏和胃): 0.8234
测试完成！
```

## 📊 数据流程图

```
BTCV数据集 (50个样本)
├── Training/ (30个样本)
│   ├── 训练集 (24个) ──→ main.py 训练
│   └── 验证集 (6个)  ──→ main.py 验证
└── Testing/ (20个样本) ──→ test.py 测试
```

## ⚙️ 关键配置参数

### 训练参数 (main.py)
```python
BATCH_SIZE = 2           # 每次训练2个patch
MAX_EPOCHS = 100         # 最大训练轮数
ROI_SIZE = (96,96,96)    # 训练patch大小
VAL_INTERVAL = 5         # 每5轮验证一次
```

### 测试参数 (test.py)
```python
ROI_SIZE = (96,96,96)       # 滑动窗口大小
SW_BATCH_SIZE = 4           # 滑动窗口批处理
VISUALIZE_SLICES = True     # 生成可视化
NUM_SLICES_TO_VISUALIZE = 5 # 可视化前5个样本
```

## 🎯 预期结果

### 训练结果
- **训练时间**: CPU约2-3小时，GPU约30-60分钟
- **最佳Dice**: 目标>0.8 (肝脏和胃综合)
- **模型文件**: `weights/best_metric_model.pth`

### 测试结果
- **分割文件**: 20个.nii.gz分割结果
- **可视化**: 5个.png对比图像
- **性能指标**: 平均Dice分数

### 分割质量预期
- **肝脏**: Dice通常>0.85 (较大器官，容易分割)
- **胃**: Dice通常0.6-0.8 (较小器官，更具挑战性)
- **综合**: 平均Dice约0.75-0.85

## 🔧 自定义配置

### 修改训练/验证分割比例
```python
# 在 dataset.py 中修改
train_val_split = 0.9  # 90%训练，10%验证
```

### 修改测试数据源
```python
# test.py 会自动选择:
# 1. 优先使用 data/Testing/img/ (如果存在)
# 2. 否则使用 data/Training/img/ (用训练数据测试)
```

### 启用详细的类别Dice
可以修改test.py获得肝脏和胃的单独Dice分数，详见代码注释。

## 📝 总结

- **main.py**: 使用24个训练样本+6个验证样本，输出最佳模型
- **test.py**: 使用20个测试样本，输出分割结果、可视化和性能评估
- **数据不重叠**: 训练、验证、测试使用不同的数据集
- **完整流程**: 从训练到测试的端到端医学图像分割系统
