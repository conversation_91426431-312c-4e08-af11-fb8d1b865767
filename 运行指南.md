# BTCV肝脏和胃分割项目运行指南

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖包
pip install -r requirements.txt

# 或者手动安装主要依赖
pip install torch torchvision monai nibabel matplotlib tqdm
```

### 2. 数据准备

确保BTCV数据集按以下结构组织：

```
data/
├── Training/
│   ├── img/          # 训练图像: img0001.nii.gz, img0002.nii.gz, ...
│   └── label/        # 训练标签: label0001.nii.gz, label0002.nii.gz, ...
└── Testing/
    └── img/          # 测试图像: img0061.nii.gz, img0062.nii.gz, ...
```

### 3. 运行项目

#### 🎯 主要命令

```bash
# 1. 系统演示（推荐先运行）
python demo.py

# 2. 开始训练
python main.py

# 3. 模型测试
python test.py

# 4. 数据可视化
python visualize.py --stats_only
```

## 📋 详细步骤

### 步骤1: 验证环境和数据

```bash
# 检查数据结构和统计信息
python visualize.py --stats_only
```

预期输出：
```
BTCV数据集统计信息
训练图像数量: 30
训练标签数量: 30
测试图像数量: 20
```

### 步骤2: 运行演示

```bash
# 完整系统演示
python demo.py
```

这会展示：
- ✅ 数据加载功能
- ✅ 数据预处理结果
- ✅ 模型架构信息
- ✅ 可视化样例

### 步骤3: 开始训练

```bash
# 3D MONAI训练
python main.py
```

训练过程：
```
使用设备: cpu
正在加载数据...
找到 30 个训练样本
训练集: 24 样本, 验证集: 6 样本
开始训练，共 100 个 epochs...

Epoch 1/100
训练损失: 1.99 → 1.80 (下降中)
每5个epoch进行一次验证
```

### 步骤4: 模型测试

```bash
# 等训练完成后运行测试
python test.py
```

测试输出：
- 分割结果保存到 `test_outputs/`
- 可视化图像
- Dice分数评估

## ⚙️ 配置参数

### 训练参数 (main.py)

```python
# 可以根据需要调整的参数
BATCH_SIZE = 2           # 批处理大小（根据内存调整）
MAX_EPOCHS = 100         # 训练轮数
LEARNING_RATE = 1e-4     # 学习率
ROI_SIZE = (96, 96, 96)  # 训练patch大小
```

### 硬件要求

- **CPU训练**: 8GB+ 内存，训练较慢但稳定
- **GPU训练**: 需要更新CUDA驱动，8GB+ 显存
- **存储**: 至少5GB空间用于数据和模型

## 📁 项目文件说明

### 核心文件
- `main.py` - 3D MONAI训练主程序
- `dataset.py` - 数据加载和预处理
- `test.py` - 模型测试和推理
- `demo.py` - 系统功能演示

### 配置文件
- `requirements.txt` - 依赖包列表
- `setup.py` - 环境安装脚本

### 工具文件
- `visualize.py` - 数据可视化工具
- `运行指南.md` - 本文档

### 输出目录
- `weights/` - 模型权重保存
- `test_outputs/` - 测试结果输出

## 🔧 常见问题解决

### 1. CUDA驱动问题
```
警告: CUDA驱动版本过旧
解决: 程序会自动使用CPU训练，或更新NVIDIA驱动
```

### 2. 内存不足
```bash
# 减小批处理大小
BATCH_SIZE = 1

# 关闭数据缓存
cache_dataset=False
```

### 3. 数据路径错误
```bash
# 检查数据结构
python -c "
import os
for root, dirs, files in os.walk('./data'):
    print(f'{root}: {len(files)} files')
"
```

### 4. 依赖包问题
```bash
# 重新安装依赖
pip install --upgrade -r requirements.txt
```

## 📊 预期结果

### 训练结果
- 训练损失: 从 ~2.0 降至 ~1.5
- 验证Dice: 目标 >0.8
- 训练时间: CPU约2-3小时

### 分割结果
- 肝脏分割精度: 高
- 胃分割精度: 中等（胃较小）
- 输出格式: NIfTI (.nii.gz)

## 🎯 使用建议

1. **首次使用**: 先运行 `python demo.py` 确认环境
2. **快速测试**: 可以减少 `MAX_EPOCHS` 到 10 进行快速验证
3. **生产使用**: 建议使用GPU并增加训练轮数
4. **结果分析**: 查看 `test_outputs/` 中的可视化结果

## 📞 技术支持

如果遇到问题：
1. 检查数据目录结构是否正确
2. 确认所有依赖包已安装
3. 查看错误日志信息
4. 尝试减小批处理大小

---

**项目特点**: 专业3D医学图像分割，基于MONAI框架，支持肝脏和胃的自动分割。
