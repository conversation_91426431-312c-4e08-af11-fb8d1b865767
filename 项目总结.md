# BTCV肝脏和胃自动分割项目 - 实现总结

## 项目概述

我们成功实现了基于BTCV数据集的肝脏(Liver)和胃(Stomach)自动分割功能，提供了两种不同的实现方案：

### 方案一：3D MONAI实现（推荐）
- **文件**: `main.py`, `dataset.py`, `test.py`
- **架构**: 3D U-Net (MONAI框架)
- **特点**: 专业医学图像分割，处理完整3D体积

### 方案二：2D切片实现
- **文件**: `train.py`, `model.py`
- **架构**: 2D U-Net (自定义实现)
- **特点**: 逐切片处理，训练速度较快

## 成功解决的问题

### 1. 数据路径和结构问题
- ✅ 修复了数据目录路径匹配
- ✅ 实现了图像-标签文件的自动配对
- ✅ 支持BTCV标准数据结构

### 2. 标签映射问题
- ✅ 正确处理BTCV多器官标签（0-13）
- ✅ 映射肝脏(6→1)和胃(7→2)
- ✅ 将其他器官设为背景(→0)

### 3. CUDA兼容性问题
- ✅ 检测并处理CUDA驱动版本过旧问题
- ✅ 自动回退到CPU训练
- ✅ 优化了CPU训练性能

### 4. 数据加载器问题
- ✅ 修复了tensor维度不匹配错误
- ✅ 实现了稳定的数据预处理流程
- ✅ 支持数据缓存和多进程加载

## 当前训练状态

### 3D MONAI训练（正在进行）
```
数据集: 30个训练样本
训练集: 24样本, 验证集: 6样本
当前进度: Epoch 3/100
训练损失: 1.99 → 1.80 (下降中)
每epoch耗时: ~1分钟
```

### 2D切片训练（已测试）
```
数据集: 1550个有效切片
训练集: 1240切片, 验证集: 310切片
训练速度: ~6-7秒/batch
```

## 项目文件说明

### 核心训练文件
- `main.py` - 3D MONAI训练主程序
- `train.py` - 2D切片训练程序
- `dataset.py` - 数据加载和预处理
- `test.py` - 模型测试和推理

### 模型和配置
- `model.py` - 2D U-Net模型定义
- `requirements.txt` - 依赖包列表
- `setup.py` - 环境安装脚本

### 工具和文档
- `visualize.py` - 数据可视化工具
- `README.md` - 详细使用说明
- `项目总结.md` - 本文档

## 技术特点

### 3D MONAI方案
- **优势**: 
  - 专业医学图像处理
  - 完整3D上下文信息
  - 先进的数据增强
  - 标准化的医学图像流程
- **适用**: 高精度分割需求

### 2D切片方案
- **优势**:
  - 训练速度快
  - 内存需求低
  - 易于调试和可视化
- **适用**: 快速原型和实验

## 数据预处理流程

1. **图像预处理**:
   - 重采样到统一间距 (1.5, 1.5, 2.0)mm
   - 强度归一化 [-175, 250] → [0, 1]
   - 前景裁剪去除背景

2. **标签处理**:
   - 多器官标签映射
   - 目标器官提取
   - One-hot编码转换

3. **数据增强**:
   - 随机翻转和旋转
   - 强度偏移
   - 随机裁剪patch

## 模型配置

### 3D U-Net (MONAI)
```python
UNet(
    spatial_dims=3,
    in_channels=1,
    out_channels=3,  # 背景、肝脏、胃
    channels=(16, 32, 64, 128, 256),
    strides=(2, 2, 2, 2)
)
```

### 损失函数
- **DiceCELoss**: Dice Loss + Cross Entropy
- **适用**: 类别不平衡的分割任务

## 使用方法

### 开始训练
```bash
# 3D MONAI训练（推荐）
python main.py

# 2D切片训练
python train.py
```

### 模型测试
```bash
python test.py
```

### 数据可视化
```bash
python visualize.py
```

## 性能指标

训练过程中监控的指标：
- **训练损失**: DiceCE Loss
- **验证Dice**: 分割精度指标
- **每类Dice**: 肝脏和胃的单独评估

## 输出结果

### 训练输出
- `weights/best_metric_model.pth` - 最佳3D模型
- `weights/best_model.pth` - 最佳2D模型

### 测试输出
- `test_outputs/*_seg.nii.gz` - 分割结果
- `test_outputs/*_visualization.png` - 可视化图像

## 下一步计划

1. **完成当前训练**: 等待3D模型训练完成
2. **性能评估**: 在测试集上评估模型性能
3. **结果分析**: 分析肝脏和胃的分割精度
4. **模型优化**: 根据结果调整超参数
5. **部署准备**: 准备模型推理接口

## 总结

我们成功实现了BTCV数据集上的肝脏和胃自动分割功能，解决了数据处理、模型训练和兼容性等多个技术问题。项目提供了完整的训练、测试和可视化工具链，支持两种不同的实现方案，可以根据具体需求选择使用。
